import React from 'react';
import { Users2, Linkedin, Mail, Calendar, Award, Target } from 'lucide-react';
import teamMember1 from '../assets/team-member1.jpg';
import teamMember2 from '../assets/team-member2.jpg';
import teamMember3 from '../assets/Virendra.jpg';
import teamMember4 from '../assets/team-aman.jpeg';
import teamMember5 from '../assets/ankit.jpeg';
import teamMember7 from '../assets/mahfooj.jpg';
import teamMember6 from '../assets/amisha.jpeg';
import teamMember8 from '../assets/swati.jpg';

const TeamSection = () => {
  const teamMembers = [
    {
      name: 'Dr. <PERSON><PERSON><PERSON>',
      role: 'Founder & CEO',
      image: teamMember1,
      bio: 'Dr. <PERSON><PERSON><PERSON>, Founder & CEO, brings 15+ years of experience, leading our team with innovation and excellence to deliver outstanding client solutions.',
      linkedin: 'https://www.linkedin.com/in/dr-<PERSON><PERSON><PERSON>-<PERSON>han-35545569/',
      experience: '15+ Years',
      expertise: 'Digital Strategy',
      achievements: 'Tech Innovation Award 2023'
    },
    {
      name: '<PERSON><PERSON>. <PERSON><PERSON>',
      role: 'Managing Director',
      image: team<PERSON>ember2,
      bio: 'As Managing Director, Er. Sadiya Anjum drives growth and innovation, leveraging her strategic insights to keep us competitive and forward-thinking.',
      linkedin: 'https://www.linkedin.com/in/sadiya-anjum-15671a16a/',
      experience: '9+ Years',
      expertise: 'Business Strategy',
      achievements: 'Business Excellence Award'
    },
    {
      name: 'Mr. Virendra Arya',
      role: 'Sr. Manager',
      image: teamMember3,
      bio: 'Mr. Virendra Arya, dedicated to cultivating a positive workplace, aligning team goals, and enhancing productivity through effective practices.',
      linkedin: 'https://www.linkedin.com/in/',
      experience: '17+ Years',
      expertise: 'Sr. Manager',
      achievements: 'Best Manager Practices'
    },
    {
      name: 'Aman Katiyar',
      role: 'Software Developer',
      image: teamMember4, // You can add a new image import for Aman
      bio: 'Aman Katiyar is a skilled software developer focused on creating innovative solutions and delivering high-quality code.',
      linkedin: 'https://www.linkedin.com/in/amanktyr',
      experience: '3+ Years',
      expertise: 'Software Development',
      achievements: 'Code Excellence Award'
    },
    {
      name: 'Ankit Yadav',
      role: 'Python Developer and Trainer',
      image: teamMember5, // You can add a new image import for Ankit
      bio: 'Ankit Yadav specializes in Python development and training, sharing knowledge and building robust applications.',
      linkedin: 'https://www.linkedin.com/in/',
      experience: '3+ Years',
      expertise: 'Python Development',
      achievements: 'Training Excellence Award'
    },
    {
      name: 'Amisha Yadav',
      role: 'Web Developer',
      image: teamMember6, // You can add a new image import for Amisha
      bio: 'Amisha Yadav is a talented web developer creating engaging and responsive web experiences for our clients.',
      linkedin: 'https://www.linkedin.com/in/',
      experience: '2+ Years',
      expertise: 'Web Development',
      achievements: 'UI/UX Innovation Award'
    },
    {
      name: 'Mahfooj Ahamad',
      role: 'Social Media Intern',
      image: teamMember7, // You can add a new image import for Mahfooj
      bio: 'Mahfooj Ahamad manages our social media presence, creating engaging content and building our online community.',
      linkedin: 'https://www.linkedin.com/in/',
      experience: '1+ Year',
      expertise: 'Social Media Marketing',
      achievements: 'Digital Engagement Award'
    },
    {
      name: 'Swati Singh',
      role: 'HR and Counselor',
      image: teamMember8, // You can add a new image import for Swati
      bio: 'Swati Singh leads our HR initiatives and provides counseling support, ensuring a positive work environment for all team members.',
      linkedin: 'https://www.linkedin.com/in/',
      experience: '3+ Years',
      expertise: 'Human Resources',
      achievements: 'Employee Satisfaction Award'
    }

  ];

  return (
    <section className="relative py-16 bg-gradient-to-b from-gray-50 to-white overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-48 h-48 bg-blue-50 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute top-0 right-0 w-48 h-48 bg-yellow-50 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-20 left-10 w-48 h-48 bg-purple-50 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="container mx-auto px-4 relative">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 px-3 py-1 bg-blue-50 rounded-full mb-4">
            <Users2 className="w-4 h-4 text-[#100562]" />
            <span className="text-sm font-medium text-[#100562]">Our Leadership</span>
          </div>
          
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Meet the Visionaries
            <span className="relative ml-2">
              Behind Our Success
              <div className="absolute left-0 -bottom-1 w-full h-1 bg-[#FF9B00] transform -skew-x-12 opacity-80"></div>
            </span>
          </h2>
          
          <p className="text-base text-gray-600 max-w-2xl mx-auto">
            Our leadership team brings together decades of experience and expertise,
            driving innovation and excellence in everything we do.
          </p>
        </div>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {teamMembers.map((member, index) => (
            <div
              key={index}
              className="group relative bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-500"
            >
              {/* Top Accent Line */}
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-[#100562] to-purple-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>
              
              {/* Member Image */}
              <div className="relative h-100 overflow-hidden">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full h-half object-cover transform group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                
                {/* Quick Info Overlay */}
                <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                  <h3 className="text-lg font-bold mb-1">{member.name}</h3>
                  <p className="text-sm text-blue-200">{member.role}</p>
                </div>
              </div>

              {/* Member Details */}
              <div className="p-4">
                <p className="text-sm text-gray-600 mb-4 line-clamp-3">{member.bio}</p>
                
                {/* Stats Grid */}
                <div className="grid grid-cols-2 gap-3 mb-4">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2 text-xs text-gray-600">
                      <Calendar className="w-3 h-3 text-blue-500" />
                      <span>Experience</span>
                    </div>
                    <p className="text-sm font-semibold text-gray-900">{member.experience}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2 text-xs text-gray-600">
                      <Target className="w-3 h-3 text-blue-500" />
                      <span>Expertise</span>
                    </div>
                    <p className="text-sm font-semibold text-gray-900">{member.expertise}</p>
                  </div>
                </div>

                {/* Achievement Badge */}
                <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-lg mb-4">
                  <Award className="w-4 h-4 text-blue-500" />
                  <span className="text-xs font-medium text-blue-900">{member.achievements}</span>
                </div>

                {/* Contact Actions */}
                <div className="flex gap-2">
                  <a
                    href={member.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 inline-flex items-center justify-center gap-1 px-3 py-2 bg-[#0A66C2] text-white text-sm rounded-lg hover:bg-[#004182] transition-colors duration-300"
                  >
                    <Linkedin className="w-3 h-3" />
                    <span>LinkedIn</span>
                  </a>
                  <a
                    href={`mailto:${member.name.toLowerCase().replace(' ', '.')}@sipherweb.com`}
                    className="flex-1 inline-flex items-center justify-center gap-1 px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors duration-300"
                  >
                    <Mail className="w-3 h-3" />
                    <span>Email</span>
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TeamSection;
