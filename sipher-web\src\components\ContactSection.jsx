import React from 'react';
import { ArrowRight, Phone, Mail, MapPin, MessageCircle } from 'lucide-react';
import teamImage from '../assets/team-3.jpeg';

const ContactSection = () => {
  return (
    <section className="relative bg-gradient-to-b from-gray-50 via-white to-gray-50 py-16 md:py-20 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, #100562 1px, transparent 0)`,
        backgroundSize: '48px 48px',
        opacity: '0.03'
      }}></div>

      <div className="container mx-auto px-4 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full mb-6">
            <MessageCircle className="w-5 h-5 text-[#100562]" />
            <span className="text-sm font-semibold text-[#100562]">Get in Touch</span>
          </div>
          
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Ready to Discuss Your
            <span className="relative ml-2">
              Next Project?
              <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FF9B00] transform -skew-x-12 opacity-80"></div>
            </span>
          </h2>
          
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Let's bring your ideas to life. Reach out to us, and we'll help create the best solution for your needs.
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          
          {/* Left Side - Contact Information */}
          <div className="space-y-8">
            {/* Contact Cards */}
            <div className="grid gap-6">
              {[
                {
                  icon: <Phone className="w-6 h-6" />,
                  title: "Call Us",
                  info: "+91 91255 45607",
                  subInfo: "Mon-Sat: 9:00 AM - 6:00 PM",
                  action: "tel:+************"
                },
                {
                  icon: <Mail className="w-6 h-6" />,
                  title: "Email Us",
                  info: "<EMAIL>",
                  subInfo: "24/7 Support Available",
                  action: "mailto:<EMAIL>"
                },
                {
                  icon: <MapPin className="w-6 h-6" />,
                  title: "Visit Us",
                  info: "Sector G, Jankipuram",
                  subInfo: "Lucknow, India",
                  action: "/contact#map"
                }
              ].map((item, index) => (
                <a
                  key={index}
                  href={item.action}
                  className="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-4"
                >
                  <div className="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center text-[#100562] group-hover:scale-110 transition-transform duration-300">
                    {item.icon}
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-1">{item.title}</h3>
                    <p className="text-[#100562] font-medium text-sm">{item.info}</p>
                    <p className="text-gray-500 text-xs">{item.subInfo}</p>
                  </div>
                </a>
              ))}
            </div>

            {/* CTA Button */}
            <div className="pt-4">
              <a
                href="/contact"
                className="group inline-flex items-center gap-3 px-8 py-4 bg-[#100562] text-white rounded-xl font-semibold hover:bg-[#FF9B00] hover:text-[#100562] transition-all duration-300 transform hover:-translate-y-1 shadow-lg hover:shadow-xl"
              >
                Get Started Today
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </a>
            </div>
          </div>

          {/* Right Side - Team Image */}
          <div className="relative">
            <div className="relative rounded-2xl overflow-hidden shadow-2xl">
              <img
                src={teamImage}
                alt="Our Team"
                className="w-full h-[500px] object-cover transform hover:scale-105 transition-transform duration-500"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
              
              {/* Overlay Content */}
              <div className="absolute bottom-6 left-6 right-6 text-white">
                <h3 className="text-2xl font-bold mb-2">Meet Our Expert Team</h3>
                <p className="text-white/90 text-sm">
                  Passionate professionals dedicated to bringing your vision to life with cutting-edge technology and creative solutions.
                </p>
              </div>
            </div>

            {/* Decorative Elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-[#FF9B00] rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-[#100562] rounded-full opacity-10 animate-pulse animation-delay-2000"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
