import React from 'react';
import { ArrowRight, Phone, Mail, MapPin, MessageCircle, Sparkles } from 'lucide-react';
import teamImage from '../assets/team-3.jpeg';

const ContactSection = () => {
  return (
    <section className="relative bg-gradient-to-br from-slate-50 via-white to-blue-50/30 py-20 md:py-28 overflow-hidden">
      {/* Enhanced Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, #100562 1px, transparent 0)`,
          backgroundSize: '60px 60px',
          opacity: '0.04'
        }}></div>
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-500/5 via-transparent to-orange-500/5"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-3 h-3 bg-[#FF9B00] rounded-full animate-bounce opacity-60"></div>
      <div className="absolute top-40 right-20 w-2 h-2 bg-[#100562] rounded-full animate-pulse opacity-40"></div>
      <div className="absolute bottom-32 left-20 w-4 h-4 bg-gradient-to-r from-[#100562] to-[#FF9B00] rounded-full animate-ping opacity-30"></div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Enhanced Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-blue-50 to-orange-50 rounded-full mb-8 shadow-sm border border-blue-100/50">
            <MessageCircle className="w-5 h-5 text-[#100562] animate-pulse" />
            <span className="text-sm font-bold text-[#100562] tracking-wide">Get in Touch</span>
            <Sparkles className="w-4 h-4 text-[#FF9B00]" />
          </div>

          <h2 className="text-4xl md:text-6xl lg:text-7xl font-black text-gray-900 mb-8 leading-tight">
            Ready to Discuss Your
            <span className="relative ml-3 inline-block">
              Next Project?
              <div className="absolute left-0 -bottom-3 w-full h-3 bg-gradient-to-r from-[#FF9B00] to-[#FF9B00]/70 transform -skew-x-12 opacity-90 rounded-sm"></div>
              <div className="absolute left-2 -bottom-1 w-full h-1 bg-[#100562] transform -skew-x-12 opacity-60 rounded-sm"></div>
            </span>
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
            Let's bring your ideas to life with innovative solutions. Reach out to us, and we'll craft the perfect digital experience for your business.
          </p>
        </div>

        {/* Enhanced Main Content Grid */}
        <div className="grid lg:grid-cols-2 gap-16 items-center">

          {/* Left Side - Enhanced Contact Information */}
          <div className="space-y-10">
            {/* Contact Cards with Enhanced Styling */}
            <div className="grid gap-8">
              {[
                {
                  icon: <Phone className="w-7 h-7" />,
                  title: "Call Us",
                  info: "+91 91255 45607",
                  subInfo: "Mon-Sat: 9:00 AM - 6:00 PM",
                  action: "tel:+919125545607",
                  gradient: "from-green-50 to-emerald-50",
                  iconBg: "bg-gradient-to-br from-green-100 to-emerald-100",
                  iconColor: "text-green-600"
                },
                {
                  icon: <Mail className="w-7 h-7" />,
                  title: "Email Us",
                  info: "<EMAIL>",
                  subInfo: "24/7 Support Available",
                  action: "mailto:<EMAIL>",
                  gradient: "from-blue-50 to-indigo-50",
                  iconBg: "bg-gradient-to-br from-blue-100 to-indigo-100",
                  iconColor: "text-[#100562]"
                },
                {
                  icon: <MapPin className="w-7 h-7" />,
                  title: "Visit Us",
                  info: "Sector G, Jankipuram",
                  subInfo: "Lucknow, India",
                  action: "/contact#map",
                  gradient: "from-orange-50 to-amber-50",
                  iconBg: "bg-gradient-to-br from-orange-100 to-amber-100",
                  iconColor: "text-[#FF9B00]"
                }
              ].map((item, index) => (
                <a
                  key={index}
                  href={item.action}
                  className={`group relative bg-gradient-to-br ${item.gradient} p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 flex items-center gap-6 border border-white/50 backdrop-blur-sm hover:-translate-y-2 hover:scale-[1.02] overflow-hidden`}
                >
                  {/* Card Background Effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  <div className={`relative w-16 h-16 ${item.iconBg} rounded-2xl flex items-center justify-center ${item.iconColor} group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg`}>
                    {item.icon}
                  </div>
                  <div className="relative">
                    <h3 className="text-xl font-black text-gray-900 mb-2 group-hover:text-[#100562] transition-colors duration-300">{item.title}</h3>
                    <p className="text-[#100562] font-bold text-base mb-1">{item.info}</p>
                    <p className="text-gray-600 text-sm font-medium">{item.subInfo}</p>
                  </div>

                  {/* Hover Arrow */}
                  <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
                    <ArrowRight className="w-5 h-5 text-[#100562]" />
                  </div>
                </a>
              ))}
            </div>

            {/* Enhanced CTA Button */}
            <div className="pt-8">
              <a
                href="/contact"
                className="group relative inline-flex items-center gap-4 px-10 py-5 bg-gradient-to-r from-[#100562] to-[#100562]/90 text-white rounded-2xl font-bold text-lg hover:from-[#FF9B00] hover:to-[#FF9B00]/90 hover:text-[#100562] transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 shadow-xl hover:shadow-2xl overflow-hidden"
              >
                {/* Button Background Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <span className="relative z-10">Get Started Today</span>
                <ArrowRight className="relative z-10 w-6 h-6 group-hover:translate-x-2 group-hover:scale-110 transition-all duration-500" />

                {/* Button Shine Effect */}
                <div className="absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent transform translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000"></div>
              </a>
            </div>
          </div>

          {/* Right Side - Enhanced Team Image */}
          <div className="relative lg:order-last">
            <div className="relative rounded-3xl overflow-hidden shadow-2xl group">
              <img
                src={teamImage}
                alt="Our Team"
                className="w-full h-[600px] object-cover transform group-hover:scale-110 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent group-hover:from-black/40 transition-all duration-500"></div>

              {/* Enhanced Overlay Content */}
              <div className="absolute bottom-8 left-8 right-8 text-white transform group-hover:translate-y-[-8px] transition-transform duration-500">
                <h3 className="text-3xl font-black mb-3 text-shadow-lg">Meet Our Expert Team</h3>
                <p className="text-white/95 text-base leading-relaxed font-medium">
                  Passionate professionals dedicated to bringing your vision to life with cutting-edge technology and creative solutions.
                </p>
              </div>

              {/* Image Border Effect */}
              <div className="absolute inset-0 rounded-3xl border-2 border-white/20 group-hover:border-[#FF9B00]/50 transition-colors duration-500"></div>
            </div>

            {/* Enhanced Decorative Elements */}
            <div className="absolute -top-6 -right-6 w-32 h-32 bg-gradient-to-br from-[#FF9B00] to-[#FF9B00]/70 rounded-full opacity-20 animate-pulse blur-sm"></div>
            <div className="absolute -bottom-6 -left-6 w-40 h-40 bg-gradient-to-br from-[#100562] to-[#100562]/70 rounded-full opacity-15 animate-pulse blur-sm" style={{ animationDelay: '2s' }}></div>
            <div className="absolute top-10 -left-4 w-6 h-6 bg-[#FF9B00] rounded-full opacity-60 animate-bounce" style={{ animationDelay: '1s' }}></div>
            <div className="absolute -top-2 right-20 w-4 h-4 bg-[#100562] rounded-full opacity-40 animate-ping" style={{ animationDelay: '3s' }}></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
